{% extends 'layout.html' %}

{% block title %}إحصائيات النظام{% endblock %}

{% block styles %}
<style>
    .card {
        margin-bottom: 20px;
    }
    .table-responsive {
        overflow-x: auto;
    }
    .accordion-button:not(.collapsed) {
        background-color: #e7f1ff;
    }
    .accordion-button {
        position: relative;
    }
    .accordion-button .badge {
        position: absolute;
        left: 20px;
    }
    .card-header {
        position: relative;
    }
    .card-header .badge {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
    }
    .table-sm td, .table-sm th {
        padding: 0.3rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>إحصائيات النظام</h5>
                </div>
                <div class="card-body">
                    <!-- الإحصائيات الرئيسية -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card text-center bg-gradient-primary text-white h-100 shadow">
                                <div class="card-body position-relative">
                                    <h1 class="display-4 fw-bold">{{ "{:,}".format(total_news) }}</h1>
                                    <p class="lead mb-0">إجمالي الأخبار</p>
                                    <i class="fas fa-newspaper fa-3x opacity-25 position-absolute bottom-0 end-0 mb-3 me-3"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card text-center bg-gradient-success text-white h-100 shadow">
                                <div class="card-body position-relative">
                                    <h1 class="display-4 fw-bold">{{ total_categories }}</h1>
                                    <p class="lead mb-0">التصنيفات</p>
                                    <i class="fas fa-tags fa-3x opacity-25 position-absolute bottom-0 end-0 mb-3 me-3"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card text-center bg-gradient-info text-white h-100 shadow">
                                <div class="card-body position-relative">
                                    <h1 class="display-4 fw-bold">{{ total_governorates }}</h1>
                                    <p class="lead mb-0">المحافظات</p>
                                    <i class="fas fa-map-marker-alt fa-3x opacity-25 position-absolute bottom-0 end-0 mb-3 me-3"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card text-center bg-gradient-warning text-white h-100 shadow">
                                <div class="card-body position-relative">
                                    <h1 class="display-4 fw-bold">{{ total_sources }}</h1>
                                    <p class="lead mb-0">مصادر الأخبار</p>
                                    <i class="fas fa-rss fa-3x opacity-25 position-absolute bottom-0 end-0 mb-3 me-3"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإحصائيات الزمنية -->
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card text-center border-primary h-100">
                                <div class="card-body">
                                    <h2 class="text-primary fw-bold">{{ today_news }}</h2>
                                    <p class="text-muted mb-1">أخبار اليوم</p>
                                    {% if today_change > 0 %}
                                        <small class="text-success"><i class="fas fa-arrow-up"></i> +{{ today_change }}</small>
                                    {% elif today_change < 0 %}
                                        <small class="text-danger"><i class="fas fa-arrow-down"></i> {{ today_change }}</small>
                                    {% else %}
                                        <small class="text-muted"><i class="fas fa-minus"></i> لا تغيير</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card text-center border-success h-100">
                                <div class="card-body">
                                    <h2 class="text-success fw-bold">{{ week_news }}</h2>
                                    <p class="text-muted mb-1">هذا الأسبوع</p>
                                    <small class="text-info">متوسط {{ week_avg }} يومياً</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card text-center border-warning h-100">
                                <div class="card-body">
                                    <h2 class="text-warning fw-bold">{{ month_news }}</h2>
                                    <p class="text-muted mb-1">آخر 30 يوم</p>
                                    <small class="text-info">متوسط {{ (month_news / 30)|round(1) }} يومياً</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card text-center border-info h-100">
                                <div class="card-body">
                                    <h2 class="text-info fw-bold">{{ (total_news / 30)|round(0)|int }}</h2>
                                    <p class="text-muted mb-1">متوسط شهري</p>
                                    <small class="text-muted">تقدير عام</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الأخبار حسب التصنيف -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-tags me-2"></i>الأخبار حسب التصنيف</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>التصنيف</th>
                                    <th>عدد الأخبار</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in category_stats %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <a href="{{ url_for('view_news_by_category', category_id=stat.id) }}">
                                            {{ stat.name }}
                                        </a>
                                    </td>
                                    <td>{{ stat.news_count }}</td>
                                    <td>{{ (stat.news_count / total_news * 100)|round(0)|int }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الأخبار حسب المحافظة -->
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>الأخبار حسب المحافظة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>المحافظة</th>
                                    <th>عدد الأخبار</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in governorate_stats %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <a href="{{ url_for('view_news_by_governorate', governorate_id=stat.id) }}">
                                            {{ stat.name }}
                                        </a>
                                    </td>
                                    <td>{{ stat.news_count }}</td>
                                    <td>{{ (stat.news_count / total_news * 100)|round(0)|int }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الأخبار حسب المصدر -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0"><i class="fas fa-rss me-2"></i>جميع مصادر الأخبار</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>المصدر</th>
                                    <th>عدد الأخبار</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in source_stats %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <a href="{{ url_for('view_news_by_source', source_name=stat.source) }}"
                                           class="text-decoration-none">
                                            {{ stat.source }}
                                        </a>
                                    </td>
                                    <td>{{ stat.news_count }}</td>
                                    <td>{{ (stat.news_count / total_news * 100)|round(0)|int }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الأخبار حسب الشهر -->
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>الأخبار حسب الشهر</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الشهر</th>
                                    <th>عدد الأخبار</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in month_stats %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ stat.month }}</td>
                                    <td>{{ stat.news_count }}</td>
                                    <td>{{ (stat.news_count / total_news * 100)|round(0)|int }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الحقول الديناميكية -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>تفاصيل التصنيفات والحقول الإضافية</h5>
                    <button class="btn btn-light btn-sm" id="printStatsBtn">
                        <i class="fas fa-print me-1"></i> طباعة الإحصائيات
                    </button>
                    <script>
                        // فتح صفحة الطباعة بطريقة مخصصة
                        document.getElementById('printStatsBtn').addEventListener('click', function() {
                            var url = '/stats';
                            var windowName = 'الإحصائيات';
                            var windowFeatures = 'width=1000,height=800,menubar=no,toolbar=no,location=no,status=no';
                            var printWindow = window.open(url, windowName, windowFeatures);

                            // تغيير عنوان الصفحة بعد فتحها
                            if (printWindow) {
                                printWindow.onload = function() {
                                    printWindow.document.title = 'الإحصائيات';
                                };
                            }
                        });
                    </script>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for category in field_stats %}
                        <div class="col-12 mb-4">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">{{ category.category_name }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="text-muted">إجمالي الأخبار: {{ category.news_count }}</h5>
                                    </div>

                                    <!-- الحقول الرقمية -->
                                    <div class="row mb-4">
                                        {% for field in category.fields %}
                                        {% if field.field_type == 'number' and field.count > 0 %}
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 border-primary shadow-sm">
                                                <div class="card-body text-center py-3">
                                                    <h2 class="text-primary fw-bold mb-1">{{ field.total }}</h2>
                                                    <p class="text-muted mb-1">{{ field.name }}</p>
                                                    <small class="text-secondary">
                                                        المتوسط: {{ field.average }} |
                                                        الاستجابة: {{ field.response_rate }}%
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                        {% endif %}
                                        {% endfor %}
                                    </div>

                                    <!-- الحقول الأخرى (قوائم منسدلة، نصوص، إلخ) -->
                                    <div class="accordion" id="accordion{{ category.category_id }}">
                                        {% for field in category.fields %}
                                        {% if field.field_type != 'number' and field.value_items|length > 0 %}
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="heading{{ field.id }}">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                                        data-bs-target="#collapse{{ field.id }}" aria-expanded="false"
                                                        aria-controls="collapse{{ field.id }}">
                                                    <strong>{{ field.name }}</strong>
                                                    <span class="badge bg-primary ms-2">{{ field.total_responses }} إجابة</span>
                                                    <span class="badge bg-success ms-1">{{ field.response_rate }}% معدل الاستجابة</span>
                                                </button>
                                            </h2>
                                            <div id="collapse{{ field.id }}" class="accordion-collapse collapse"
                                                 aria-labelledby="heading{{ field.id }}" data-bs-parent="#accordion{{ category.category_id }}">
                                                <div class="accordion-body">
                                                    <div class="table-responsive">
                                                        <table class="table table-sm table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>القيمة</th>
                                                                    <th>العدد</th>
                                                                    <th>النسبة</th>
                                                                    <th>التمثيل البصري</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                {% for item in field.value_items %}
                                                                <tr>
                                                                    <td>{{ item.value }}</td>
                                                                    <td>{{ item.value_count }}</td>
                                                                    <td>{{ item.percentage }}%</td>
                                                                    <td>
                                                                        <div class="progress" style="height: 20px;">
                                                                            <div class="progress-bar bg-primary" role="progressbar"
                                                                                 style="width: {{ item.percentage }}%"
                                                                                 aria-valuenow="{{ item.percentage }}"
                                                                                 aria-valuemin="0" aria-valuemax="100">
                                                                                {{ item.percentage }}%
                                                                            </div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                {% endfor %}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
