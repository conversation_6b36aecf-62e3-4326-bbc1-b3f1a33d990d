{% extends "layout.html" %}

{% block content %}
<div class="container py-4">
    <!-- شريط التنقل -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('view_news') }}">الأخبار</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ news.title|truncate(30) }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- القسم الرئيسي للخبر -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-body p-0">
                    <!-- صورة الخبر (يمكن إضافتها لاحقاً) -->
                    <div class="bg-primary text-white p-4 text-center">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-light text-primary">{{ news.category.name }}</span>
                            <span class="small"><i class="fas fa-calendar-alt me-1"></i> {{ news.date|format_date_arabic('full_levant') }}</span>
                        </div>
                        <h1 class="mt-3 mb-0">{{ news.title }}</h1>
                    </div>

                    <!-- معلومات الخبر -->
                    <div class="p-4">
                        <div class="d-flex justify-content-between align-items-center mb-4 text-muted small">
                            <div>
                                <i class="fas fa-map-marker-alt me-1"></i> {{ news.governorate.name }}
                                <span class="mx-2">|</span>
                                <i class="fas fa-newspaper me-1"></i>
                                {% if news.source_url %}
                                    <a href="{{ news.source_url }}" target="_blank" class="text-decoration-none">
                                        {{ news.source }}
                                        <i class="fas fa-external-link-alt ms-1 small text-primary"></i>
                                    </a>
                                {% else %}
                                    {{ news.source }}
                                {% endif %}
                            </div>
                        </div>

                        <!-- محتوى الخبر -->
                        <div class="news-content mb-4">
                            {{ news.content|nl2br|safe }}
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between mt-4">
                            <div>
                                <a href="{{ url_for('edit_news', news_id=news.id) }}" class="btn btn-sm btn-warning me-2">
                                    <i class="fas fa-edit"></i> تعديل الخبر
                                </a>
                                <button class="btn btn-sm btn-danger"
                                        data-bs-toggle="modal"
                                        data-bs-target="#deleteNewsModal">
                                    <i class="fas fa-trash"></i> حذف الخبر
                                </button>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-primary me-2" onclick="window.print()">
                                    <i class="fas fa-print"></i> طباعة
                                </button>
                                <button class="btn btn-sm btn-outline-primary me-2" onclick="shareOnFacebook()">
                                    <i class="fab fa-facebook-f"></i> مشاركة
                                </button>
                                <button class="btn btn-sm btn-outline-primary me-2" onclick="shareOnTwitter()">
                                    <i class="fab fa-twitter"></i> تغريد
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="shareOnWhatsApp()">
                                    <i class="fab fa-whatsapp"></i> واتساب
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- القسم الجانبي -->
        <div class="col-lg-4">
            <!-- معلومات إضافية -->
            {% if field_data %}
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات وبيانات</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        {% for field in field_data %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>{{ field.name }}</span>
                            <span class="badge bg-primary rounded-pill">{{ field.value }}</span>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
            {% endif %}

            <!-- معلومات التصنيف -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-tag me-2"></i>معلومات التصنيف</h5>
                </div>
                <div class="card-body">
                    <h6 class="card-title">{{ news.category.name }}</h6>
                    {% if news.category.description %}
                    <p class="card-text">{{ news.category.description }}</p>
                    {% else %}
                    <p class="card-text text-muted">لا يوجد وصف للتصنيف</p>
                    {% endif %}
                </div>
            </div>

            <!-- معلومات المحافظة -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-map-marked-alt me-2"></i>المحافظة</h5>
                </div>
                <div class="card-body">
                    <h6 class="card-title">{{ news.governorate.name }}</h6>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .news-content {
        font-size: 1.1rem;
        line-height: 1.8;
        text-align: justify;
    }

    @media print {
        .breadcrumb, .card-header, .col-lg-4, .d-flex.justify-content-end {
            display: none !important;
        }
        .col-lg-8 {
            width: 100% !important;
        }
    }
</style>
{% endblock %}

<!-- نافذة تأكيد حذف الخبر -->
<div class="modal fade" id="deleteNewsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">تأكيد حذف الخبر</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الخبر: <strong>{{ news.title }}</strong>؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> تحذير: لا يمكن التراجع عن هذه العملية.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteNewsForm" action="{{ url_for('delete_news', news_id=news.id) }}" method="POST">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    function shareOnFacebook() {
        var url = encodeURIComponent(window.location.href);
        var title = encodeURIComponent("{{ news.title }}");
        window.open('https://www.facebook.com/sharer/sharer.php?u=' + url + '&t=' + title, '_blank');
    }

    function shareOnTwitter() {
        var url = encodeURIComponent(window.location.href);
        var text = encodeURIComponent("{{ news.title }}");
        window.open('https://twitter.com/intent/tweet?url=' + url + '&text=' + text, '_blank');
    }

    function shareOnWhatsApp() {
        var url = encodeURIComponent(window.location.href);
        var text = encodeURIComponent("{{ news.title }}");
        window.open('https://api.whatsapp.com/send?text=' + text + ' ' + url, '_blank');
    }
</script>
{% endblock %}
