// ملف تشخيص مشكلة زر الحذف
// يمكن تشغيله في console المتصفح

console.log('🔍 بدء تشخيص زر الحذف...');

// 1. فحص تحميل المكتبات
function checkLibraries() {
    console.log('\n=== فحص المكتبات ===');
    
    if (typeof jQuery === 'undefined') {
        console.error('❌ jQuery غير محمل!');
        return false;
    } else {
        console.log('✅ jQuery محمل - الإصدار:', $.fn.jquery);
    }
    
    if (typeof bootstrap === 'undefined') {
        console.error('❌ Bootstrap غير محمل!');
        return false;
    } else {
        console.log('✅ Bootstrap محمل');
    }
    
    return true;
}

// 2. فحص أزرار الحذف
function checkDeleteButtons() {
    console.log('\n=== فحص أزرار الحذف ===');
    
    const deleteButtons = $('.delete-news-btn');
    console.log('عدد أزرار الحذف الموجودة:', deleteButtons.length);
    
    if (deleteButtons.length === 0) {
        console.warn('⚠️ لا توجد أزرار حذف في الصفحة!');
        return false;
    }
    
    deleteButtons.each(function(index) {
        const btn = $(this);
        const newsId = btn.data('id');
        const newsTitle = btn.data('title');
        
        console.log(`زر الحذف #${index + 1}:`, {
            element: this,
            newsId: newsId,
            newsTitle: newsTitle,
            hasClass: btn.hasClass('delete-news-btn'),
            isVisible: btn.is(':visible'),
            isEnabled: !btn.prop('disabled')
        });
        
        if (!newsId) {
            console.error(`❌ زر الحذف #${index + 1} لا يحتوي على data-id`);
        }
        
        if (!newsTitle) {
            console.error(`❌ زر الحذف #${index + 1} لا يحتوي على data-title`);
        }
    });
    
    return true;
}

// 3. فحص النافذة المنبثقة
function checkModal() {
    console.log('\n=== فحص النافذة المنبثقة ===');
    
    const modal = $('#deleteNewsModal');
    if (modal.length === 0) {
        console.error('❌ النافذة المنبثقة غير موجودة!');
        return false;
    }
    
    console.log('✅ النافذة المنبثقة موجودة');
    
    const newsTitle = $('#newsTitle');
    const deleteForm = $('#deleteNewsForm');
    
    if (newsTitle.length === 0) {
        console.error('❌ عنصر #newsTitle غير موجود!');
    } else {
        console.log('✅ عنصر #newsTitle موجود');
    }
    
    if (deleteForm.length === 0) {
        console.error('❌ نموذج #deleteNewsForm غير موجود!');
    } else {
        console.log('✅ نموذج #deleteNewsForm موجود');
    }
    
    return true;
}

// 4. فحص معالجات الأحداث
function checkEventHandlers() {
    console.log('\n=== فحص معالجات الأحداث ===');
    
    // فحص معالج النقر
    const events = $._data(document, 'events');
    if (events && events.click) {
        console.log('✅ يوجد معالجات نقر مسجلة على document');
        
        const clickHandlers = events.click.filter(handler => 
            handler.selector && handler.selector.includes('delete-news-btn')
        );
        
        if (clickHandlers.length > 0) {
            console.log('✅ يوجد معالج نقر لأزرار الحذف');
        } else {
            console.error('❌ لا يوجد معالج نقر لأزرار الحذف!');
        }
    } else {
        console.error('❌ لا توجد معالجات نقر مسجلة!');
    }
}

// 5. اختبار النقر اليدوي
function testManualClick() {
    console.log('\n=== اختبار النقر اليدوي ===');
    
    const firstDeleteBtn = $('.delete-news-btn').first();
    if (firstDeleteBtn.length === 0) {
        console.error('❌ لا يوجد زر حذف للاختبار!');
        return;
    }
    
    console.log('محاولة النقر على أول زر حذف...');
    
    try {
        firstDeleteBtn.trigger('click');
        console.log('✅ تم تنفيذ النقر');
    } catch (error) {
        console.error('❌ خطأ في النقر:', error);
    }
}

// 6. إضافة معالج مؤقت للاختبار
function addTemporaryHandler() {
    console.log('\n=== إضافة معالج مؤقت ===');
    
    $(document).off('click.debug', '.delete-news-btn');
    $(document).on('click.debug', '.delete-news-btn', function(e) {
        e.preventDefault();
        console.log('🔴 تم النقر على زر الحذف (معالج مؤقت)!');
        
        const newsId = $(this).data('id');
        const newsTitle = $(this).data('title');
        
        console.log('معرف الخبر:', newsId);
        console.log('عنوان الخبر:', newsTitle);
        
        alert('تم النقر على زر الحذف!\nمعرف الخبر: ' + newsId + '\nالعنوان: ' + newsTitle);
    });
    
    console.log('✅ تم إضافة معالج مؤقت');
}

// تشغيل جميع الاختبارات
function runAllTests() {
    console.log('🚀 تشغيل جميع اختبارات تشخيص زر الحذف');
    console.log('='.repeat(50));
    
    const libsOk = checkLibraries();
    const buttonsOk = checkDeleteButtons();
    const modalOk = checkModal();
    
    if (libsOk && buttonsOk && modalOk) {
        checkEventHandlers();
        addTemporaryHandler();
        
        console.log('\n🎯 يمكنك الآن اختبار النقر على أزرار الحذف');
        console.log('أو تشغيل testManualClick() لاختبار تلقائي');
    } else {
        console.log('\n❌ يوجد مشاكل تحتاج إلى حل قبل المتابعة');
    }
}

// تشغيل الاختبارات عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
} else {
    runAllTests();
}

// إتاحة الوظائف للاستخدام اليدوي
window.debugDeleteButton = {
    checkLibraries,
    checkDeleteButtons,
    checkModal,
    checkEventHandlers,
    testManualClick,
    addTemporaryHandler,
    runAllTests
};
