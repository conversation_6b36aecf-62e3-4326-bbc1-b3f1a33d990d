#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة أخبار تجريبية لاختبار النظام
"""

import sys
import os
from datetime import datetime, date, timedelta

# إضافة المجلد الحالي إلى مسار البحث
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def add_sample_data():
    """إضافة بيانات تجريبية"""
    
    try:
        # استيراد النماذج
        from app.models.relational_models import db, News, Category, Governorate, init_governorates
        from config import Config
        from flask import Flask
        
        # إنشاء تطبيق Flask
        app = Flask(__name__)
        app.config.from_object(Config)
        app.config['SQLALCHEMY_DATABASE_URI'] = app.config['SQLALCHEMY_DATABASE_URI'].replace('app.db', 'relational_app.db')
        
        # تهيئة قاعدة البيانات
        db.init_app(app)
        
        with app.app_context():
            # إنشاء الجداول
            db.create_all()
            
            # تهيئة المحافظات
            init_governorates(db.session)
            
            # إضافة تصنيفات تجريبية
            categories_data = [
                {'name': 'أخبار عامة', 'description': 'أخبار عامة ومتنوعة'},
                {'name': 'أخبار اقتصادية', 'description': 'أخبار الاقتصاد والمال'},
                {'name': 'أخبار سياسية', 'description': 'أخبار السياسة والحكومة'},
                {'name': 'أخبار رياضية', 'description': 'أخبار الرياضة والألعاب'},
                {'name': 'أخبار ثقافية', 'description': 'أخبار الثقافة والفنون'}
            ]
            
            for cat_data in categories_data:
                existing_cat = Category.query.filter_by(name=cat_data['name']).first()
                if not existing_cat:
                    category = Category(name=cat_data['name'], description=cat_data['description'])
                    db.session.add(category)
            
            db.session.commit()
            
            # جلب التصنيفات والمحافظات
            categories = Category.query.all()
            governorates = Governorate.query.all()
            
            if not categories or not governorates:
                print("❌ لا توجد تصنيفات أو محافظات")
                return False
            
            # إضافة أخبار تجريبية
            sample_news = [
                {
                    'title': 'افتتاح مشروع تطوير البنية التحتية في بغداد',
                    'content': 'تم افتتاح مشروع جديد لتطوير البنية التحتية في العاصمة بغداد، والذي يهدف إلى تحسين الخدمات المقدمة للمواطنين وتطوير الشوارع والجسور الرئيسية.',
                    'source': 'وكالة الأنباء العراقية',
                    'source_url': 'https://example.com/news1',
                    'date': date.today()
                },
                {
                    'title': 'ارتفاع أسعار النفط في الأسواق العالمية',
                    'content': 'شهدت أسعار النفط ارتفاعاً ملحوظاً في الأسواق العالمية اليوم، مما يؤثر إيجابياً على الاقتصاد العراقي والإيرادات النفطية للبلاد.',
                    'source': 'صحيفة الاقتصاد',
                    'source_url': 'https://example.com/news2',
                    'date': date.today() - timedelta(days=1)
                },
                {
                    'title': 'انطلاق مهرجان بابل الثقافي السنوي',
                    'content': 'انطلقت فعاليات مهرجان بابل الثقافي السنوي بمشاركة واسعة من الفنانين والأدباء من مختلف المحافظات العراقية، ويستمر المهرجان لمدة أسبوع كامل.',
                    'source': 'دائرة الثقافة والفنون',
                    'source_url': 'https://example.com/news3',
                    'date': date.today() - timedelta(days=2)
                },
                {
                    'title': 'فوز المنتخب العراقي في مباراة ودية',
                    'content': 'حقق المنتخب العراقي لكرة القدم فوزاً مهماً في المباراة الودية التي أقيمت على ملعب الشعب الدولي، وسط حضور جماهيري كبير.',
                    'source': 'الاتحاد العراقي لكرة القدم',
                    'source_url': 'https://example.com/news4',
                    'date': date.today() - timedelta(days=3)
                },
                {
                    'title': 'إطلاق حملة تشجير في محافظة البصرة',
                    'content': 'أطلقت محافظة البصرة حملة واسعة لتشجير المناطق الحضرية والريفية، بهدف تحسين البيئة ومواجهة التغيرات المناخية.',
                    'source': 'مجلس محافظة البصرة',
                    'source_url': 'https://example.com/news5',
                    'date': date.today() - timedelta(days=4)
                }
            ]
            
            # إضافة الأخبار
            for i, news_data in enumerate(sample_news):
                # اختيار تصنيف ومحافظة عشوائياً
                category = categories[i % len(categories)]
                governorate = governorates[i % len(governorates)]
                
                # التحقق من عدم وجود الخبر مسبقاً
                existing_news = News.query.filter_by(title=news_data['title']).first()
                if not existing_news:
                    news = News(
                        title=news_data['title'],
                        content=news_data['content'],
                        source=news_data['source'],
                        source_url=news_data['source_url'],
                        date=news_data['date'],
                        category_id=category.id,
                        governorate_id=governorate.id
                    )
                    db.session.add(news)
                    print(f"✅ تم إضافة الخبر: {news_data['title']}")
                else:
                    print(f"⚠️  الخبر موجود مسبقاً: {news_data['title']}")
            
            db.session.commit()
            
            # إحصائيات
            total_news = News.query.count()
            total_categories = Category.query.count()
            total_governorates = Governorate.query.count()
            
            print(f"\n📊 إحصائيات قاعدة البيانات:")
            print(f"   📰 عدد الأخبار: {total_news}")
            print(f"   📂 عدد التصنيفات: {total_categories}")
            print(f"   🏛️  عدد المحافظات: {total_governorates}")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 إضافة بيانات تجريبية لنظام الرصد الإعلامي")
    print("=" * 50)
    
    success = add_sample_data()
    
    if success:
        print("\n🎉 تم إضافة البيانات التجريبية بنجاح!")
        print("يمكنك الآن تشغيل التطبيق واختبار زر الحذف")
    else:
        print("\n❌ فشل في إضافة البيانات التجريبية")
