#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل نظام الرصد الإعلامي
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار البحث
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == '__main__':
    try:
        # استيراد التطبيق من app.py (الملف الرئيسي)
        import app
        flask_app = app.app
        
        print("🚀 بدء تشغيل نظام الرصد الإعلامي...")
        print("📍 الرابط: http://127.0.0.1:5000")
        print("⏹️  للإيقاف: اضغط Ctrl+C")
        print("-" * 50)
        
        # تشغيل التطبيق
        flask_app.run(
            host='127.0.0.1',
            port=5000,
            debug=True,
            use_reloader=True
        )
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)
