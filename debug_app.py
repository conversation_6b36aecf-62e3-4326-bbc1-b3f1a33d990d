#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشخيص مشاكل التطبيق
"""

import sys
import os

def check_dependencies():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    required_modules = [
        'flask', 'flask_sqlalchemy', 'flask_migrate', 
        'datetime', 'json', 'sqlite3', 'docx'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")
            missing.append(module)
    
    return missing

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص الملفات...")
    
    required_files = [
        'app.py', 'config.py', 'requirements.txt',
        'app/templates/layout.html',
        'app/templates/view_news.html',
        'app/templates/news_details.html',
        'app/static/js/main.js'
    ]
    
    missing = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing.append(file_path)
    
    return missing

def test_import():
    """اختبار استيراد التطبيق"""
    print("\n🔧 اختبار استيراد التطبيق...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # اختبار استيراد config
        from config import Config
        print("✅ config.py")
        
        # اختبار استيراد النماذج
        from app.models.relational_models import db, News
        print("✅ models")
        
        # اختبار إنشاء التطبيق
        from flask import Flask
        app = Flask(__name__)
        print("✅ Flask app creation")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

if __name__ == '__main__':
    print("🚀 تشخيص نظام الرصد الإعلامي")
    print("=" * 50)
    
    # فحص المتطلبات
    missing_modules = check_dependencies()
    
    # فحص الملفات
    missing_files = check_files()
    
    # اختبار الاستيراد
    import_success = test_import()
    
    print("\n" + "=" * 50)
    print("📊 ملخص التشخيص:")
    
    if missing_modules:
        print(f"❌ مكتبات مفقودة: {', '.join(missing_modules)}")
    else:
        print("✅ جميع المكتبات متوفرة")
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
    else:
        print("✅ جميع الملفات متوفرة")
    
    if import_success:
        print("✅ الاستيراد ناجح")
    else:
        print("❌ فشل في الاستيراد")
    
    if not missing_modules and not missing_files and import_success:
        print("\n🎉 النظام جاهز للتشغيل!")
    else:
        print("\n⚠️  يوجد مشاكل تحتاج إلى حل")
