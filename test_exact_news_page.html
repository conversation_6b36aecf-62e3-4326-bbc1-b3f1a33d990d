<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الرصد الإعلامي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container py-4">
        <!-- شريط التنقل -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="#">الرئيسية</a></li>
                <li class="breadcrumb-item active" aria-current="page">الأخبار</li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0"><i class="fas fa-newspaper me-2"></i>قائمة الأخبار</h2>
            <div>
                <button id="testDeleteBtn" class="btn btn-warning me-2" onclick="testDeleteFunction()">
                    <i class="fas fa-bug me-1"></i> اختبار الحذف
                </button>
                <a href="#" class="btn btn-success me-2">
                    <i class="fas fa-print me-1"></i> طباعة الأخبار
                </a>
                <a href="#" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-1"></i> إضافة خبر جديد
                </a>
            </div>
        </div>

        <!-- محاكاة الأخبار المجمعة حسب التاريخ -->
        <div class="col-12 mb-3 mt-4">
            <div class="date-separator">
                <h3 class="date-heading">
                    <i class="fas fa-calendar-day me-2"></i>
                    أخبار اليوم
                </h3>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover news-table">
                <thead class="table-light">
                    <tr>
                        <th scope="col" width="50%">عنوان الخبر</th>
                        <th scope="col" width="15%">التصنيف</th>
                        <th scope="col" width="15%">المصدر</th>
                        <th scope="col" width="20%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="news-row">
                        <td>
                            <a href="#" class="news-title-link">
                                افتتاح مشروع تطوير البنية التحتية في بغداد
                            </a>
                            <div class="news-meta small text-muted mt-1">
                                <i class="fas fa-map-marker-alt me-1"></i> بغداد
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-primary">أخبار عامة</span>
                        </td>
                        <td>
                            <a href="https://example.com/news1" target="_blank" class="text-decoration-none source-link">
                                وكالة الأنباء العراقية
                                <i class="fas fa-external-link-alt ms-1 small"></i>
                            </a>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="#" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="#" class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-danger delete-news-btn"
                                        data-id="1"
                                        data-title="افتتاح مشروع تطوير البنية التحتية في بغداد"
                                        type="button">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="news-row">
                        <td>
                            <a href="#" class="news-title-link">
                                ارتفاع أسعار النفط في الأسواق العالمية
                            </a>
                            <div class="news-meta small text-muted mt-1">
                                <i class="fas fa-map-marker-alt me-1"></i> البصرة
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-success">أخبار اقتصادية</span>
                        </td>
                        <td>
                            صحيفة الاقتصاد
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="#" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="#" class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-danger delete-news-btn"
                                        data-id="2"
                                        data-title="ارتفاع أسعار النفط في الأسواق العالمية"
                                        type="button">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- نافذة تأكيد حذف الخبر -->
    <div class="modal fade" id="deleteNewsModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">تأكيد حذف الخبر</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف الخبر: <strong id="newsTitle"></strong>؟</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> تحذير: لا يمكن التراجع عن هذه العملية.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteNewsForm" action="" method="POST">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery (يجب تحميله قبل Bootstrap) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            console.log('🔍 صفحة عرض الأخبار - بدء التشخيص');
            console.log('jQuery loaded:', typeof jQuery !== 'undefined');
            console.log('Bootstrap loaded:', typeof bootstrap !== 'undefined');
            
            // فحص أزرار الحذف
            var deleteButtons = $('.delete-news-btn');
            console.log('عدد أزرار الحذف الموجودة:', deleteButtons.length);
            
            if (deleteButtons.length === 0) {
                console.warn('⚠️ لا توجد أزرار حذف في الصفحة!');
            }
            
            // تهيئة نافذة حذف الخبر
            $(document).on('click', '.delete-news-btn', function(e) {
                e.preventDefault();
                console.log('🔴 تم النقر على زر الحذف!');
                
                var newsId = $(this).data('id');
                var newsTitle = $(this).data('title');

                console.log('معرف الخبر:', newsId);
                console.log('عنوان الخبر:', newsTitle);
                
                if (!newsId) {
                    console.error('❌ معرف الخبر غير موجود!');
                    alert('خطأ: معرف الخبر غير موجود!');
                    return;
                }
                
                if (!newsTitle) {
                    console.error('❌ عنوان الخبر غير موجود!');
                    newsTitle = 'خبر بدون عنوان';
                }
                
                $('#newsTitle').text(newsTitle);
                $('#deleteNewsForm').attr('action', '/news/delete/' + newsId);
                
                // إظهار النافذة المنبثقة يدوياً
                try {
                    var deleteModal = new bootstrap.Modal(document.getElementById('deleteNewsModal'));
                    deleteModal.show();
                    console.log('✅ تم إظهار النافذة المنبثقة');
                } catch (error) {
                    console.error('❌ خطأ في إظهار النافذة المنبثقة:', error);
                    alert('خطأ في إظهار نافذة التأكيد!');
                }
            });

            // تأكيد الحذف
            $('#deleteNewsForm').on('submit', function(e) {
                e.preventDefault(); // منع الإرسال الفعلي للاختبار
                console.log('🔴 تم إرسال نموذج الحذف');
                alert('تم تأكيد الحذف - سيتم الحذف في التطبيق الفعلي');
                
                // إخفاء النافذة
                var deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteNewsModal'));
                if (deleteModal) {
                    deleteModal.hide();
                }
            });
        });
        
        // وظيفة اختبار الحذف
        function testDeleteFunction() {
            console.log('🧪 اختبار وظيفة الحذف...');
            
            var firstDeleteBtn = $('.delete-news-btn').first();
            if (firstDeleteBtn.length === 0) {
                alert('لا توجد أزرار حذف للاختبار!');
                console.error('❌ لا توجد أزرار حذف');
                return;
            }
            
            console.log('محاولة النقر على أول زر حذف...');
            firstDeleteBtn.trigger('click');
        }
    </script>
</body>
</html>
