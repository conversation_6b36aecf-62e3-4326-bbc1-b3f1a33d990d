<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة الأخبار</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>اختبار صفحة الأخبار - زر الحذف</h2>
        
        <!-- محاكاة جدول الأخبار -->
        <div class="table-responsive">
            <table class="table table-hover news-table">
                <thead class="table-light">
                    <tr>
                        <th scope="col" width="50%">عنوان الخبر</th>
                        <th scope="col" width="15%">التصنيف</th>
                        <th scope="col" width="15%">المصدر</th>
                        <th scope="col" width="20%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="news-row">
                        <td>
                            <a href="#" class="news-title-link">خبر تجريبي للاختبار</a>
                            <div class="news-meta small text-muted mt-1">
                                <i class="fas fa-map-marker-alt me-1"></i> بغداد
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-primary">أخبار عامة</span>
                        </td>
                        <td>
                            <a href="#" target="_blank" class="text-decoration-none source-link">
                                مصدر تجريبي
                                <i class="fas fa-external-link-alt ms-1 small"></i>
                            </a>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="#" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="#" class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-danger delete-news-btn"
                                        data-id="123"
                                        data-title="خبر تجريبي للاختبار"
                                        type="button">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="news-row">
                        <td>
                            <a href="#" class="news-title-link">خبر تجريبي آخر</a>
                            <div class="news-meta small text-muted mt-1">
                                <i class="fas fa-map-marker-alt me-1"></i> البصرة
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-success">أخبار اقتصادية</span>
                        </td>
                        <td>مصدر آخر</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="#" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="#" class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-danger delete-news-btn"
                                        data-id="456"
                                        data-title="خبر تجريبي آخر"
                                        type="button">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- نافذة تأكيد الحذف -->
        <div class="modal fade" id="deleteNewsModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">تأكيد حذف الخبر</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>هل أنت متأكد من حذف الخبر: <strong id="newsTitle"></strong>؟</p>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> تحذير: لا يمكن التراجع عن هذه العملية.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <form id="deleteNewsForm" action="" method="POST">
                            <button type="submit" class="btn btn-danger">حذف</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- منطقة الرسائل -->
        <div id="messages" class="mt-3"></div>
    </div>

    <!-- jQuery (يجب تحميله قبل Bootstrap) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            console.log('=== تشخيص صفحة الأخبار ===');
            console.log('Document ready');
            console.log('jQuery version:', $.fn.jquery);
            console.log('Bootstrap loaded:', typeof bootstrap !== 'undefined');
            
            // عدد أزرار الحذف
            var deleteButtons = $('.delete-news-btn');
            console.log('عدد أزرار الحذف الموجودة:', deleteButtons.length);
            
            // فحص كل زر حذف
            deleteButtons.each(function(index) {
                var btn = $(this);
                console.log('زر الحذف #' + (index + 1) + ':', {
                    id: btn.data('id'),
                    title: btn.data('title'),
                    hasClass: btn.hasClass('delete-news-btn')
                });
            });
            
            // تهيئة نافذة حذف الخبر
            $(document).on('click', '.delete-news-btn', function(e) {
                e.preventDefault();
                var newsId = $(this).data('id');
                var newsTitle = $(this).data('title');

                console.log('🔴 Delete button clicked!');
                console.log('News ID:', newsId);
                console.log('News title:', newsTitle);
                
                // إضافة رسالة في الصفحة
                $('#messages').html('<div class="alert alert-info">تم النقر على زر الحذف للخبر: ' + newsTitle + ' (ID: ' + newsId + ')</div>');
                
                $('#newsTitle').text(newsTitle);
                $('#deleteNewsForm').attr('action', '/news/delete/' + newsId);
                
                // إظهار النافذة المنبثقة يدوياً
                try {
                    var deleteModal = new bootstrap.Modal(document.getElementById('deleteNewsModal'));
                    deleteModal.show();
                    console.log('✅ Modal shown successfully');
                } catch (error) {
                    console.error('❌ Error showing modal:', error);
                }
            });

            // تأكيد الحذف
            $('#deleteNewsForm').on('submit', function(e) {
                e.preventDefault(); // منع الإرسال الفعلي للاختبار
                console.log('🔴 Delete form submitted');
                
                $('#messages').html('<div class="alert alert-success">تم تأكيد الحذف - سيتم الحذف في التطبيق الفعلي</div>');
                
                // إخفاء النافذة
                var deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteNewsModal'));
                if (deleteModal) {
                    deleteModal.hide();
                }
            });
            
            // اختبار النقر المباشر
            console.log('=== اختبار النقر المباشر ===');
            setTimeout(function() {
                console.log('محاولة النقر على أول زر حذف...');
                $('.delete-news-btn').first().trigger('click');
            }, 2000);
        });
    </script>
</body>
</html>
