<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر الحذف</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>اختبار زر الحذف</h2>
        
        <!-- زر الحذف -->
        <button class="btn btn-danger delete-news-btn" 
                data-id="123" 
                data-title="خبر تجريبي"
                type="button">
            <i class="fas fa-trash"></i> حذف الخبر
        </button>
        
        <!-- نافذة تأكيد الحذف -->
        <div class="modal fade" id="deleteNewsModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">تأكيد حذف الخبر</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>هل أنت متأكد من حذف الخبر: <strong id="newsTitle"></strong>؟</p>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> تحذير: لا يمكن التراجع عن هذه العملية.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <form id="deleteNewsForm" action="#" method="POST">
                            <button type="submit" class="btn btn-danger">حذف</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery (يجب تحميله قبل Bootstrap) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            console.log('Document ready');
            console.log('jQuery version:', $.fn.jquery);
            console.log('Bootstrap loaded:', typeof bootstrap !== 'undefined');
            
            // تهيئة نافذة حذف الخبر
            $(document).on('click', '.delete-news-btn', function(e) {
                e.preventDefault();
                var newsId = $(this).data('id');
                var newsTitle = $(this).data('title');

                console.log('Delete button clicked for news ID:', newsId);
                console.log('News title:', newsTitle);
                
                $('#newsTitle').text(newsTitle);
                $('#deleteNewsForm').attr('action', '/news/delete/' + newsId);
                
                // إظهار النافذة المنبثقة يدوياً
                var deleteModal = new bootstrap.Modal(document.getElementById('deleteNewsModal'));
                deleteModal.show();
            });

            // تأكيد الحذف
            $('#deleteNewsForm').on('submit', function(e) {
                e.preventDefault(); // منع الإرسال الفعلي للاختبار
                console.log('Delete form submitted');
                alert('تم النقر على زر الحذف - سيتم الحذف في التطبيق الفعلي');
                
                // إخفاء النافذة
                var deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteNewsModal'));
                deleteModal.hide();
            });
        });
    </script>
</body>
</html>
